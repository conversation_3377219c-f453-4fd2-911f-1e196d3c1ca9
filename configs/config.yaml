# 动态任务图谱差分更新实验配置文件

# 数据路径配置
data:
  raw_data_path: "/data2/syd_data/Breakfast_Data/breakfast_data"
  raw_label_path: "/data2/syd_data/Breakfast_Data/segmentation_coarse"
  npy_data_path: "/data2/syd_data/Breakfast_Data/breakfast_data_npy"
  npy_label_path: "/data2/syd_data/Breakfast_Data/segmentation_coarse_npy"
  label_map_path: "/data2/syd_data/Breakfast_Data/label_map.json"

# 输出路径配置
output:
  base_path: "/data2/syd_data/Breakfast_Data/Outputs"

# Memory文件夹配置
memory:
  base_path: "/data2/syd_data/Breakfast_Data/Code/Memory"
  experiment_logs: "experiment_logs"
  config_backups: "config_backups"
  implementation_notes: "implementation_notes"

  # 日志配置
  enable_logging: true
  log_level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  max_log_files: 10
  log_rotation_size: "10MB"

# 训练配置
training:
  train_splits: ["s1", "s2", "s3"]
  test_splits: ["s4"]
  max_epochs: 10
  batch_size: 32
  learning_rate: 0.001
  random_seed: 42
  early_stopping: false

  # 优化器配置
  optimizer: "adam"  # adam, sgd, adamw
  weight_decay: 1e-4

  # 学习率调度
  lr_scheduler: "cosine"  # cosine, step, plateau
  lr_step_size: 5
  lr_gamma: 0.5

  # 正则化
  l2_regularization: 1e-4
  label_smoothing: 0.1
  class_balancing: false

  # 早停
  patience: 5
  min_delta: 1e-4

# 模型配置
model:
  feature_dim: 64
  num_classes: 48  # 将在运行时动态更新
  mlp_hidden_dims: [512, 256]
  activation: "relu"  # relu, gelu, leaky_relu, swish
  dropout_rate: 0.1

  # 静态模型配置
  use_temperature: false

  # 动态模型配置
  use_gating: false
  residual_weight_init: 0.1

  # 边权重配置
  laplace_smoothing_alpha: 1.0

# 评估配置
evaluation:
  iou_thresholds: [0.5, 0.75, 0.9]
  bootstrap_samples: 500
  confidence_interval: 0.95
  exclude_sil: true

# 可视化配置
visualization:
  style: "seaborn-v0_8"
  dpi: 300
  figsize: [12, 8]
  save_format: "png"

# 数据预处理配置
preprocessing:
  fusion_method: "mean"  # mean, concat, max
  validation_tolerance: 1e-6
  overwrite_existing: true

# 硬件配置
hardware:
  use_cuda: true
  num_workers: 4
  pin_memory: true
  
  # 序列处理参数
  min_sequence_length: 5
  max_sequence_length: 2000

# 日志配置
logging:
  log_every_n_steps: 1    # 日志间隔
  save_top_k: 5
  monitor: val_segment_iou
  mode: max

# 实验配置
experiment:
  name: cereals_dynamic_graph
  version: null
  tags: ["cereals", "dynamic_graph", "breakfast", "plan_min"]

# 调试配置
debug:
  fast_dev_run: false
  overfit_batches: 0
  limit_train_batches: 1.0
  limit_val_batches: 1.0

# Hydra配置
hydra:
  run:
    dir: ${output_dir}/hydra_runs/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: true
