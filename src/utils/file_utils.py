"""文件操作工具"""
import os
import json
import pickle
import numpy as np
import pandas as pd
import torch
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import logging
import shutil

class FileManager:
    """完整的文件管理器，支持Memory文件夹集成"""

    def __init__(self, base_output_path: str, experiment_name: str = None, memory_config: Dict = None):
        self.base_output_path = Path(base_output_path)
        self.timestamp_id = self._generate_timestamp_id()
        self.experiment_name = experiment_name or "experiment"

        # Memory文件夹配置
        self.memory_config = memory_config or {}
        self.memory_base_path = Path(self.memory_config.get('base_path', './Memory'))

        # 创建实验目录
        self.experiment_dir = self.base_output_path / f"{self.experiment_name}_{self.timestamp_id}"

        # 设置日志（包括Memory文件夹日志）
        self._setup_logging()

        # 创建目录结构
        self.create_output_directories()

        # 备份配置到Memory文件夹
        self._backup_config_to_memory()
    
    def _generate_timestamp_id(self) -> str:
        """生成时间戳ID (YYYYMMDD-HHMMSS)"""
        return datetime.now().strftime("%Y%m%d-%H%M%S")
    
    def _setup_logging(self):
        """设置日志系统，包括Memory文件夹日志"""
        # 实验目录日志
        log_dir = self.experiment_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)

        log_file = log_dir / f"experiment_{self.timestamp_id}.log"

        # Memory文件夹日志
        memory_log_dir = self.memory_base_path / "experiment_logs"
        memory_log_dir.mkdir(parents=True, exist_ok=True)

        memory_log_file = memory_log_dir / f"experiment_{self.timestamp_id}.log"

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.FileHandler(memory_log_file),  # 同时写入Memory文件夹
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Experiment started: {self.experiment_name}")
        self.logger.info(f"Timestamp ID: {self.timestamp_id}")
        self.logger.info(f"Output directory: {self.experiment_dir}")
        self.logger.info(f"Memory directory: {self.memory_base_path}")
    
    def create_output_directories(self):
        """创建所有必要的输出目录"""
        directories = [
            "Action_Prototype/Model_parameters",
            "Action_Prototype/Raw_data", 
            "Action_Prototype/Visualization",
            "Edge_Weight/Model_parameters",
            "Edge_Weight/Raw_data",
            "Edge_Weight/Visualization", 
            "Static/Model_parameters",
            "Static/Raw_data",
            "Static/Visualization",
            "Dynamic/Model_parameters", 
            "Dynamic/Raw_data",
            "Dynamic/Visualization",
            "Evaluation_Result",
            "logs",
            "configs"
        ]
        
        for dir_path in directories:
            full_path = self.experiment_dir / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created directory: {full_path}")
    
    def get_timestamped_filename(self, base_name: str, extension: str) -> str:
        """生成带时间戳的文件名"""
        return f"{base_name}_{self.timestamp_id}.{extension}"
    
    def save_model_parameters(self, model: Union[torch.nn.Module, Dict], 
                            model_type: str, 
                            filename: str) -> Path:
        """保存模型参数"""
        save_dir = self.experiment_dir / model_type / "Model_parameters"
        save_path = save_dir / self.get_timestamped_filename(filename, "pt")
        
        if isinstance(model, torch.nn.Module):
            torch.save(model.state_dict(), save_path)
        elif isinstance(model, dict):
            torch.save(model, save_path)
        else:
            raise ValueError(f"Unsupported model type: {type(model)}")
        
        self.logger.info(f"Saved model parameters: {save_path}")
        return save_path
    
    def save_raw_data(self, data: Any, model_type: str, filename: str, 
                     format: str = 'pickle') -> Path:
        """保存原始数据"""
        save_dir = self.experiment_dir / model_type / "Raw_data"
        
        if format == 'pickle':
            save_path = save_dir / self.get_timestamped_filename(filename, "pkl")
            with open(save_path, 'wb') as f:
                pickle.dump(data, f)
        elif format == 'json':
            save_path = save_dir / self.get_timestamped_filename(filename, "json")
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif format == 'csv':
            save_path = save_dir / self.get_timestamped_filename(filename, "csv")
            if isinstance(data, pd.DataFrame):
                data.to_csv(save_path, index=False)
            elif isinstance(data, np.ndarray):
                pd.DataFrame(data).to_csv(save_path, index=False)
            else:
                raise ValueError("CSV format requires DataFrame or ndarray")
        elif format == 'numpy':
            save_path = save_dir / self.get_timestamped_filename(filename, "npy")
            np.save(save_path, data)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        self.logger.info(f"Saved raw data: {save_path}")
        return save_path
    
    def save_visualization(self, fig, model_type: str, filename: str) -> Path:
        """保存可视化图表"""
        save_dir = self.experiment_dir / model_type / "Visualization"
        save_path = save_dir / self.get_timestamped_filename(filename, "png")
        
        fig.savefig(save_path, dpi=300, bbox_inches='tight')
        self.logger.info(f"Saved visualization: {save_path}")
        return save_path
    
    def save_evaluation_results(self, results: Dict, filename: str) -> Path:
        """保存评估结果"""
        save_dir = self.experiment_dir / "Evaluation_Result"
        
        # 保存详细结果（JSON格式）
        json_path = save_dir / self.get_timestamped_filename(f"{filename}_detailed", "json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 保存汇总结果（CSV格式）
        if 'summary' in results:
            csv_data = []
            for metric_name, metric_data in results['summary'].items():
                csv_data.append({
                    'metric_name': metric_name,
                    'point_estimate': metric_data.get('mean', metric_data.get('point_estimate', 0)),
                    'ci_lower_bound': metric_data.get('ci_lower', 0),
                    'ci_upper_bound': metric_data.get('ci_upper', 0)
                })
            
            csv_path = save_dir / self.get_timestamped_filename(f"{filename}_summary", "csv")
            pd.DataFrame(csv_data).to_csv(csv_path, index=False)
            
            self.logger.info(f"Saved evaluation results: {json_path}, {csv_path}")
            return json_path
        
        self.logger.info(f"Saved evaluation results: {json_path}")
        return json_path
    
    def save_config(self, config: Dict, filename: str = "config") -> Path:
        """保存配置文件"""
        save_dir = self.experiment_dir / "configs"
        save_path = save_dir / self.get_timestamped_filename(filename, "yaml")
        
        import yaml
        with open(save_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        self.logger.info(f"Saved config: {save_path}")
        return save_path
    
    def load_model_parameters(self, model_type: str, filename: str) -> Dict:
        """加载模型参数"""
        load_dir = self.experiment_dir / model_type / "Model_parameters"
        
        # 查找匹配的文件
        pattern = f"{filename}_*.pt"
        matching_files = list(load_dir.glob(pattern))
        
        if not matching_files:
            raise FileNotFoundError(f"No model file found matching pattern: {pattern}")
        
        # 使用最新的文件
        latest_file = max(matching_files, key=lambda x: x.stat().st_mtime)
        
        model_data = torch.load(latest_file, map_location='cpu')
        self.logger.info(f"Loaded model parameters: {latest_file}")
        
        return model_data
    
    def load_raw_data(self, model_type: str, filename: str, format: str = 'pickle') -> Any:
        """加载原始数据"""
        load_dir = self.experiment_dir / model_type / "Raw_data"
        
        if format == 'pickle':
            pattern = f"{filename}_*.pkl"
        elif format == 'json':
            pattern = f"{filename}_*.json"
        elif format == 'csv':
            pattern = f"{filename}_*.csv"
        elif format == 'numpy':
            pattern = f"{filename}_*.npy"
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        matching_files = list(load_dir.glob(pattern))
        if not matching_files:
            raise FileNotFoundError(f"No data file found matching pattern: {pattern}")
        
        latest_file = max(matching_files, key=lambda x: x.stat().st_mtime)
        
        if format == 'pickle':
            with open(latest_file, 'rb') as f:
                data = pickle.load(f)
        elif format == 'json':
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        elif format == 'csv':
            data = pd.read_csv(latest_file)
        elif format == 'numpy':
            data = np.load(latest_file)
        
        self.logger.info(f"Loaded raw data: {latest_file}")
        return data
    
    def create_experiment_summary(self) -> Path:
        """创建实验总结报告"""
        summary_path = self.experiment_dir / "experiment_summary.md"
        
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"# Experiment Summary\n\n")
            f.write(f"**Experiment Name:** {self.experiment_name}\n")
            f.write(f"**Timestamp ID:** {self.timestamp_id}\n")
            f.write(f"**Start Time:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("## Directory Structure\n\n")
            for root, dirs, files in os.walk(self.experiment_dir):
                level = root.replace(str(self.experiment_dir), '').count(os.sep)
                indent = ' ' * 2 * level
                f.write(f"{indent}- {os.path.basename(root)}/\n")
                subindent = ' ' * 2 * (level + 1)
                for file in files[:5]:  # 只显示前5个文件
                    f.write(f"{subindent}- {file}\n")
                if len(files) > 5:
                    f.write(f"{subindent}- ... ({len(files) - 5} more files)\n")
        
        self.logger.info(f"Created experiment summary: {summary_path}")
        return summary_path

    def _backup_config_to_memory(self):
        """备份配置到Memory文件夹"""
        if not self.memory_config.get('enable_logging', True):
            return

        backup_dir = self.memory_base_path / "config_backups" / "config_versions"
        backup_dir.mkdir(parents=True, exist_ok=True)

        # 备份当前实验的配置信息
        backup_file = backup_dir / f"config_{self.timestamp_id}.json"
        backup_info = {
            "experiment_name": self.experiment_name,
            "timestamp_id": self.timestamp_id,
            "experiment_dir": str(self.experiment_dir),
            "backup_time": datetime.now().isoformat()
        }

        with open(backup_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)

        self.logger.info(f"Backed up config to Memory: {backup_file}")

    def log_to_memory(self, log_type: str, message: str, data: Any = None):
        """记录信息到Memory文件夹的特定日志"""
        if not self.memory_config.get('enable_logging', True):
            return

        log_dir = self.memory_base_path / "experiment_logs" / f"{log_type}_logs"
        log_dir.mkdir(parents=True, exist_ok=True)

        log_file = log_dir / f"{log_type}_{self.timestamp_id}.log"

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)

        # 如果有数据，保存为JSON
        if data is not None:
            data_file = log_dir / f"{log_type}_data_{self.timestamp_id}.json"
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def save_implementation_note(self, note_type: str, title: str, content: str):
        """保存实现笔记到Memory文件夹"""
        notes_dir = self.memory_base_path / "implementation_notes"
        notes_dir.mkdir(parents=True, exist_ok=True)

        note_file = notes_dir / f"{note_type}_{self.timestamp_id}.md"

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        note_content = f"# {title}\n\n"
        note_content += f"**时间**: {timestamp}\n"
        note_content += f"**实验**: {self.experiment_name}\n"
        note_content += f"**时间戳**: {self.timestamp_id}\n\n"
        note_content += content

        with open(note_file, 'w', encoding='utf-8') as f:
            f.write(note_content)

        self.logger.info(f"Saved implementation note: {note_file}")
        return note_file
