#!/usr/bin/env python3
"""
TXT到NPY格式转换脚本 - txt_to_npy.py
根据IDEA.md第1章规范实现

主要功能：
1. 递归扫描所有.txt文件
2. 实现多视角融合（1-4个视角）
3. 帧同步处理
4. 数据一致性验证
5. 生成固定64维特征向量
6. 保存为.npy格式并覆盖旧文件
"""

import os
import sys
import numpy as np
import json
import logging
import yaml
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入新的工具模块
from src.utils.data_loader import MultiViewFusion, LabelMapper, DataValidator
from src.utils.file_utils import FileManager


class TxtToNpyConverter:
    """TXT到NPY转换器 - 基于新架构重构"""

    def __init__(self, config: Dict):
        self.config = config
        self.setup_logging()

        # 初始化组件
        self.multi_view_fusion = MultiViewFusion(config['preprocessing']['fusion_method'])
        self.label_mapper = LabelMapper(config['data']['label_map_path'])
        self.data_validator = DataValidator(config['preprocessing']['validation_tolerance'])

        # 路径配置
        self.raw_data_path = Path(config['data']['raw_data_path'])
        self.raw_label_path = Path(config['data']['raw_label_path'])
        self.npy_data_path = Path(config['data']['npy_data_path'])
        self.npy_label_path = Path(config['data']['npy_label_path'])

        # 转换统计
        self.conversion_stats = {
            'total_files': 0,
            'successful_conversions': 0,
            'failed_conversions': 0,
            'errors': []
        }

    def setup_logging(self):
        """设置日志"""
        log_path = self.raw_data_path.parent / 'Code' / 'txt_to_npy.log'
        log_path.parent.mkdir(parents=True, exist_ok=True)

        logging.basicConfig(
            filename=log_path,
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            filemode='w'  # 覆盖之前的日志
        )

        # 同时输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)

        self.logger = logging.getLogger(__name__)
        self.logger.info("TXT到NPY转换器初始化完成")

    def convert_features(self, split: str, task: str) -> bool:
        """转换特征文件"""
        try:
            # 构建路径
            task_dir = self.raw_data_path / split / task
            if not task_dir.exists():
                self.logger.warning(f"Task directory not found: {task_dir}")
                return False

            # 处理多视角数据
            fused_features, frame_indices = self.multi_view_fusion.process_task_views(task_dir)

            if fused_features.size == 0:
                self.logger.warning(f"No features extracted for {split}/{task}")
                return False

            # 确保输出目录存在
            output_dir = self.npy_data_path / split
            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存融合后的特征
            output_path = output_dir / f"{task}.npy"
            np.save(output_path, fused_features)

            self.logger.info(f"Converted features: {split}/{task} -> {fused_features.shape}")
            return True

        except Exception as e:
            error_msg = f"Failed to convert features for {split}/{task}: {str(e)}"
            self.logger.error(error_msg)
            self.conversion_stats['errors'].append(error_msg)
            return False

    def convert_labels(self, split: str, task: str) -> bool:
        """转换标签文件"""
        try:
            # 构建路径
            label_file = self.raw_label_path / f"{split}_label" / task / f"{task}.txt"
            if not label_file.exists():
                self.logger.warning(f"Label file not found: {label_file}")
                return False

            # 读取标签文件
            segments = []
            with open(label_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        start_frame, end_frame, action_id = self.label_mapper.parse_segment_line(line)
                        segments.append((start_frame, end_frame, action_id))

            if not segments:
                self.logger.warning(f"No segments found in {label_file}")
                return False

            # 转换为帧级标签
            max_frame = max(seg[1] for seg in segments)
            frame_labels = np.zeros(max_frame + 1, dtype=np.int32)

            for start_frame, end_frame, action_id in segments:
                frame_labels[start_frame:end_frame + 1] = action_id

            # 确保输出目录存在
            output_dir = self.npy_label_path / f"{split}_label"
            output_dir.mkdir(parents=True, exist_ok=True)

            # 保存标签
            output_path = output_dir / f"{task}.npy"
            np.save(output_path, frame_labels)

            self.logger.info(f"Converted labels: {split}/{task} -> {len(frame_labels)} frames")
            return True

        except Exception as e:
            error_msg = f"Failed to convert labels for {split}/{task}: {str(e)}"
            self.logger.error(error_msg)
            self.conversion_stats['errors'].append(error_msg)
            return False

    def discover_tasks(self) -> List[Tuple[str, str]]:
        """发现所有需要转换的任务"""
        tasks = []

        # 扫描数据目录
        for split_dir in self.raw_data_path.iterdir():
            if split_dir.is_dir():
                split_name = split_dir.name

                for task_dir in split_dir.iterdir():
                    if task_dir.is_dir():
                        task_name = task_dir.name

                        # 检查是否有txt文件
                        txt_files = list(task_dir.glob("*.txt"))
                        if txt_files:
                            tasks.append((split_name, task_name))

        self.logger.info(f"Discovered {len(tasks)} tasks to convert")
        return tasks


    def run(self):
        """运行转换过程"""
        self.logger.info("开始TXT到NPY转换...")

        # 发现所有任务
        tasks = self.discover_tasks()

        if not tasks:
            self.logger.warning("未发现任何需要转换的任务")
            return

        # 转换每个任务
        for split, task in tasks:
            self.conversion_stats['total_files'] += 1

            # 转换特征
            feature_success = self.convert_features(split, task)

            # 转换标签
            label_success = self.convert_labels(split, task)

            if feature_success and label_success:
                self.conversion_stats['successful_conversions'] += 1
                self.logger.info(f"✅ 成功转换: {split}/{task}")
            else:
                self.conversion_stats['failed_conversions'] += 1
                self.logger.warning(f"❌ 转换失败: {split}/{task}")

        # 保存标签映射
        self.label_mapper.save_map()

        # 输出统计信息
        total = self.conversion_stats['total_files']
        success = self.conversion_stats['successful_conversions']
        failed = self.conversion_stats['failed_conversions']

        self.logger.info(f"转换完成: {success}/{total} 成功, {failed} 失败")

        if self.conversion_stats['errors']:
            self.logger.error("转换过程中的错误:")
            for error in self.conversion_stats['errors']:
                self.logger.error(f"  - {error}")


def load_config() -> Dict:
    """加载配置文件"""
    config_path = Path(__file__).parent / "configs" / "config.yaml"

    if not config_path.exists():
        # 创建默认配置
        default_config = {
            'data': {
                'raw_data_path': "/data2/syd_data/Breakfast_Data/breakfast_data",
                'raw_label_path': "/data2/syd_data/Breakfast_Data/segmentation_coarse",
                'npy_data_path': "/data2/syd_data/Breakfast_Data/breakfast_data_npy",
                'npy_label_path': "/data2/syd_data/Breakfast_Data/segmentation_coarse_npy",
                'label_map_path': "/data2/syd_data/Breakfast_Data/label_map.json"
            },
            'preprocessing': {
                'fusion_method': "mean",
                'validation_tolerance': 1e-6,
                'overwrite_existing': True
            }
        }
        return default_config

    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def main():
    """主函数"""
    print("=" * 80)
    print("🔄 TXT到NPY格式转换脚本")
    print("根据IDEA.md规范实现多视角融合、帧同步和标签映射")
    print("=" * 80)

    try:
        # 加载配置
        config = load_config()

        # 创建转换器
        converter = TxtToNpyConverter(config)

        # 运行转换
        converter.run()

        print("✅ 数据转换完成!")

    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
