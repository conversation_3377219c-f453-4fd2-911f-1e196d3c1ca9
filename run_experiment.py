#!/usr/bin/env python3
"""
动态任务图谱差分更新实验 - 完整运行脚本

使用方法:
    python run_experiment.py --config configs/config.yaml --name my_experiment

    或者使用默认配置:
    python run_experiment.py
"""

import argparse
import sys
from pathlib import Path
import yaml

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.utils.file_utils import FileManager

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="动态任务图谱差分更新实验",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        '--config',
        type=str,
        default='configs/config.yaml',
        help='配置文件路径'
    )

    parser.add_argument(
        '--name',
        type=str,
        default=None,
        help='实验名称（可选）'
    )

    parser.add_argument(
        '--steps',
        nargs='+',
        choices=['data', 'prototype', 'edge', 'static', 'dynamic', 'eval', 'all'],
        default=['all'],
        help='要执行的实验步骤'
    )

    parser.add_argument(
        '--resume',
        action='store_true',
        help='从上次中断的地方继续实验'
    )

    parser.add_argument(
        '--dry-run',
        action='store_true',
        help='仅验证配置，不执行实验'
    )

    return parser.parse_args()

def validate_config(config_path: str) -> bool:
    """验证配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        # 检查必需的配置项
        required_sections = ['data', 'output', 'training', 'model', 'evaluation']
        for section in required_sections:
            if section not in config:
                print(f"错误: 配置文件缺少必需的节: {section}")
                return False

        # 检查数据路径
        data_paths = [
            config['data']['raw_data_path'],
            config['data']['raw_label_path']
        ]

        for path in data_paths:
            if not Path(path).exists():
                print(f"警告: 数据路径不存在: {path}")

        print("配置文件验证通过")
        return True

    except Exception as e:
        print(f"配置文件验证失败: {e}")
        return False


class ExperimentManager:
    """完整的实验管理器"""

    def __init__(self, config_path: str, experiment_name: str = None):
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 验证Memory文件夹
        self._validate_memory_folder()

        # 设置随机种子
        self._set_random_seeds(self.config['training']['random_seed'])

        # 初始化组件（包含Memory配置）
        memory_config = self.config.get('memory', {})
        self.file_manager = FileManager(
            self.config['output']['base_path'],
            experiment_name,
            memory_config
        )

        # 保存配置
        self.file_manager.save_config(self.config)

        # 设备配置
        import torch
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.file_manager.logger.info(f"Using device: {self.device}")

        # 记录实验开始到Memory
        self.file_manager.log_to_memory(
            "experiment",
            "实验开始",
            {
                "experiment_name": experiment_name,
                "config_path": config_path,
                "device": str(self.device)
            }
        )

        # 实验状态
        self.experiment_state = {
            'data_converted': False,
            'prototypes_trained': False,
            'edge_weights_trained': False,
            'static_model_trained': False,
            'dynamic_model_trained': False,
            'evaluation_completed': False
        }

    def _set_random_seeds(self, seed: int):
        """设置随机种子以确保可重复性"""
        import numpy as np
        import torch

        np.random.seed(seed)
        torch.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

    def _validate_memory_folder(self):
        """验证Memory文件夹结构"""
        memory_path = Path(__file__).parent / "Memory"

        if not memory_path.exists():
            raise FileNotFoundError(f"Memory文件夹不存在: {memory_path}")

        # 检查核心文件
        required_files = ["IDEA.md", "Agent.md"]
        for file_name in required_files:
            file_path = memory_path / file_name
            if not file_path.exists():
                raise FileNotFoundError(f"Memory文件夹缺少核心文件: {file_path}")

        # 检查子目录
        required_dirs = ["experiment_logs", "config_backups", "implementation_notes"]
        for dir_name in required_dirs:
            dir_path = memory_path / dir_name
            if not dir_path.exists():
                print(f"警告: Memory子目录不存在，将自动创建: {dir_path}")
                dir_path.mkdir(parents=True, exist_ok=True)

        print(f"✅ Memory文件夹验证通过: {memory_path}")

    def run_full_experiment(self) -> dict:
        """运行完整的实验流程"""
        results = {}

        try:
            # 1. 数据预处理
            self.file_manager.logger.info("=== Starting Data Preprocessing ===")
            self._run_data_preprocessing()
            self.experiment_state['data_converted'] = True

            # 2. 训练原型特征
            self.file_manager.logger.info("=== Starting Prototype Training ===")
            prototype_results = self._run_prototype_training()
            results['prototype_training'] = prototype_results
            self.experiment_state['prototypes_trained'] = True

            # 3. 训练边权重
            self.file_manager.logger.info("=== Starting Edge Weight Training ===")
            edge_weight_results = self._run_edge_weight_training()
            results['edge_weight_training'] = edge_weight_results
            self.experiment_state['edge_weights_trained'] = True

            # 4. 训练静态模型
            self.file_manager.logger.info("=== Starting Static Model Training ===")
            static_results = self._run_static_model_training()
            results['static_training'] = static_results
            self.experiment_state['static_model_trained'] = True

            # 5. 训练动态模型
            self.file_manager.logger.info("=== Starting Dynamic Model Training ===")
            dynamic_results = self._run_dynamic_model_training()
            results['dynamic_training'] = dynamic_results
            self.experiment_state['dynamic_model_trained'] = True

            # 6. 模型评估
            self.file_manager.logger.info("=== Starting Model Evaluation ===")
            evaluation_results = self._run_model_evaluation()
            results['evaluation'] = evaluation_results
            self.experiment_state['evaluation_completed'] = True

            # 7. 生成实验报告
            self.file_manager.logger.info("=== Generating Experiment Report ===")
            self._generate_experiment_report(results)

            self.file_manager.logger.info("=== Experiment Completed Successfully ===")

        except Exception as e:
            self.file_manager.logger.error(f"Experiment failed: {str(e)}")
            import traceback
            self.file_manager.logger.error(traceback.format_exc())
            results['error'] = str(e)
            results['traceback'] = traceback.format_exc()

        finally:
            # 保存实验状态
            self.file_manager.save_raw_data(
                self.experiment_state,
                "Evaluation_Result",
                "experiment_state",
                format='json'
            )

            # 创建实验总结
            self.file_manager.create_experiment_summary()

        return results

    def _run_data_preprocessing(self):
        """运行数据预处理"""
        from txt_to_npy import TxtToNpyConverter

        converter = TxtToNpyConverter(self.config)
        converter.run()

    def _run_prototype_training(self) -> dict:
        """运行原型特征训练"""
        from Train.Train_Action_Prototype import ActionPrototypeTrainer

        trainer = ActionPrototypeTrainer(self.config)
        trainer.file_manager = self.file_manager  # 使用统一的文件管理器
        return trainer.train()

    def _run_edge_weight_training(self) -> dict:
        """运行边权重训练"""
        # 这里需要实现边权重训练器
        self.file_manager.logger.info("Edge weight training - placeholder")
        return {'status': 'completed'}

    def _run_static_model_training(self) -> dict:
        """运行静态模型训练"""
        # 这里需要实现静态模型训练器
        self.file_manager.logger.info("Static model training - placeholder")
        return {'status': 'completed'}

    def _run_dynamic_model_training(self) -> dict:
        """运行动态模型训练"""
        # 这里需要实现动态模型训练器
        self.file_manager.logger.info("Dynamic model training - placeholder")
        return {'status': 'completed'}

    def _run_model_evaluation(self) -> dict:
        """运行模型评估"""
        # 这里需要实现模型评估器
        self.file_manager.logger.info("Model evaluation - placeholder")
        return {'status': 'completed'}

    def _generate_experiment_report(self, results: dict):
        """生成实验报告"""
        report_path = self.file_manager.experiment_dir / "experiment_report.md"

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 动态任务图谱差分更新实验报告\n\n")
            f.write(f"**实验时间:** {self.file_manager.timestamp_id}\n")
            f.write(f"**配置文件:** {self.config}\n\n")

            # 实验状态
            f.write("## 实验执行状态\n\n")
            for step, completed in self.experiment_state.items():
                status = "✅ 完成" if completed else "❌ 未完成"
                f.write(f"- {step}: {status}\n")
            f.write("\n")

        self.file_manager.logger.info(f"Generated experiment report: {report_path}")


def main():
    """主函数"""
    print("=" * 80)
    print("🔄 动态任务图谱差分更新实验")
    print("=" * 80)

    args = parse_arguments()

    # 验证配置文件
    if not validate_config(args.config):
        sys.exit(1)

    if args.dry_run:
        print("配置验证完成，退出（dry-run模式）")
        return

    try:
        # 创建实验管理器
        experiment_manager = ExperimentManager(
            config_path=args.config,
            experiment_name=args.name
        )

        print(f"实验目录: {experiment_manager.file_manager.experiment_dir}")
        print(f"时间戳ID: {experiment_manager.file_manager.timestamp_id}")

        # 运行实验
        if 'all' in args.steps:
            results = experiment_manager.run_full_experiment()
        else:
            # 运行指定步骤（这里可以扩展为支持单独步骤）
            results = experiment_manager.run_full_experiment()

        # 输出结果摘要
        if 'error' not in results:
            print("\n" + "=" * 80)
            print("实验完成!")
            print("=" * 80)
        else:
            print("\n" + "=" * 80)
            print("实验失败!")
            print("=" * 80)
            print(f"错误: {results['error']}")

    except KeyboardInterrupt:
        print("\n实验被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n实验运行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
